.path-mod-scorm .top {
    vertical-align: top;
}

.path-mod-scorm .scorm-left {
    text-align: left;
}

.path-mod-scorm .scorm-right {
    text-align: right;
}

.path-mod-scorm .scoframe {
    position: relative;
    width: 100%;
    height: 100%;
}

.ios #scormpage #scorm_content {
    -webkit-overflow-scrolling: touch;
    overflow: scroll;
}

#page-mod-scorm-player #scormtop {
    position: relative;
    width: 100%;
    height: 30px;
}

#page-mod-scorm-player #scormbrowse {
    position: absolute;
    left: 5px;
    top: 0;
}

#page-mod-scorm-player #scormnav {
    position: absolute;
    right: 5px;
    text-align: center;
    top: 3px;
    width: 100%;
}

#page-mod-scorm-player #scormbox {
    width: 74%;
    height: 100%;
    position: absolute;
    right: 0;
    top: 0;
}

#page-mod-scorm-player #scormpage {
    position: relative;
    width: 100%;
    height: 100%;
}

#page-mod-scorm-player #scormpage #toctree {
    position: relative;
    width: 100%;
}

#page-mod-scorm-player #tocbox {
    position: relative;
    left: 0;
    width: 100%;
    height: 100%;
    font-size: 0.8em;
}

#page-mod-scorm-player #toctree {
    overflow: visible;
}

#page-mod-scorm-player #tochead {
    position: relative;
    text-align: center;
    top: 3px;
    height: 30px;
}

#page-mod-scorm-player #scormpage .scoframe {
    border: 0;
}

#page-mod-scorm-player #scormpage #scorm_object {
    border: none;
    width: 98%;
    height: 98%;
}

#page-mod-scorm-player #scormpage #scorm_object.scorm_nav_under_content {
    height: 95%;
}

#page-mod-scorm-player #scormpage #scorm_content {
    height: 100%;
}

#page-mod-scorm-player #scormpage #scorm_toc {
    position: relative;
}

#page-mod-scorm-player #scormpage #scorm_toc_title {
    font-size: 1.2em;
    font-weight: bold;
}

#page-mod-scorm-player #scormpage #scorm_tree {
    border-right: 5px solid rgb(239, 245, 255);
}

#page-mod-scorm-player #scormpage #scorm_navpanel {
    text-align: center;
}

#page-mod-scorm-player .toc,
#page-mod-scorm-player .no-toc {
    width: 100%;
}

#page-mod-scorm-player .structlist {
    list-style-type: none;
    white-space: nowrap;
}

#page-mod-scorm-player .structurelist {
    position: relative;
    list-style-type: none;
    width: 96%;
    margin: 0;
    padding: 0;
}

#page-mod-scorm-player .structurelist ul {
    padding-left: 0.5em;
    margin-left: 0.5em;
}

#page-mod-scorm-player #scormpage #scorm_toc.disabled,
#page-mod-scorm-player #scormpage #scorm_toc.loading,
#page-mod-scorm-player #scormpage #scorm_toc_toggle.disabled,
#page-mod-scorm-player #scormpage #scorm_toc_toggle.loading {
    display: none;
}

#page-mod-scorm-view .structurelist {
    list-style-type: none;
    white-space: nowrap;
}

#page-mod-scorm-view .structurelist {
    list-style-type: none;
    white-space: nowrap;
}

#page-mod-scorm-view .exceededmaxattempts {
    color: #c00;
}

#page-mod-scorm-player #altfinishlink {
    font-size: 140%;
    border: 0;
    padding: 0;
}

#page-mod-scorm-player #scormmode {
    float: left;
    border: 0;
}

#page-mod-scorm-player.pagelayout-popup #page-content .region-content {
    padding: 0;
}

#page-mod-scorm-player.pagelayout-popup #page-wrapper {
    width: 100%;
}

#page-mod-scorm-player .yui-layout-scroll div.yui-layout-bd {
    overflow: visible;
}

#page-mod-scorm-player .yui-layout-unit-left div.yui-layout-bd {
    overflow: auto;
}

.path-mod-scorm.forcejavascript .toc {
    display: none;
}

.path-mod-scorm.forcejavascript #scormpage #tocbox {
    display: none;
}

.path-mod-scorm.jsenabled .forcejavascriptmessage {
    display: none;
}

.path-mod-scorm.jsenabled .toc {
    display: block;
}

.path-mod-scorm.jsenabled #scormpage #tocbox {
    display: block;
}

#page-mod-scorm-report-userreporttracks table .c1 {
    word-wrap: break-word;
    word-break: break-all;
}

#page-mod-scorm-report .scormattemptcounts {
    clear: left;
    text-align: center;
    display: inline;
    margin-left: 20%;
}

#page-mod-scorm-player #scormpage span.yui3-treeview-icon {
    display: none;
}

#page-mod-scorm-player #scormpage li.yui3-treeview-has-children > div.yui3-treeview-row > span.yui3-treeview-icon {
    display: block;
}

#page-mod-scorm-player #scormpage div.yui3-u-1,
#page-mod-scorm-player #scormpage div.yui3-u-3-4,
#page-mod-scorm-player #scormpage div.yui3-u-1-5,
#page-mod-scorm-player #scormpage div.yui3-u-1-24 {
    display: inline-block;
    *display: inline; /* stylelint-disable-line */
    zoom: 1;
    letter-spacing: normal;
    word-spacing: normal;
    vertical-align: top;
    text-rendering: auto;
}

#page-mod-scorm-player #scormpage div.yui3-u-1 {
    display: block;
}

#page-mod-scorm-player #scormpage div.yui3-u-3-4 {
    width: 75%;
}

#page-mod-scorm-player #scormpage div.yui3-u-1-5 {
    width: 20%;
}

#page-mod-scorm-player #scormpage div.yui3-u-1-24 {
    width: 4.1666%;
}

#page-mod-scorm-player #scormpage div.yui3-g-r {
    letter-spacing: normal;
    word-spacing: -0.43em;
}

#scorm_layout {
    margin-bottom: 50px;
}

/**
* Opera as of 12 on Windows needs word-spacing.
* The ".opera-only" selector is used to prevent actual prefocus styling
* and is not required in markup.
*/
#page-mod-scorm-player .opera-only :-o-prefocus,
#page-mod-scorm-player #scormpage div.yui3-g-r img {
    max-width: 100%;
}
